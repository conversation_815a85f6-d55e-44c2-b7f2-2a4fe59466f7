<!--
	@component PolicyDetailModal

	A comprehensive policy detail modal component that displays detailed information
	about an insurance policy including benefits, coverage, claims history, and contract conditions.

	This component works directly with raw policy details data from the TPA API workflow
	and provides a tabbed interface for viewing policy details and claims.

	@example
	```svelte
	<PolicyDetailModal
		bind:isOpen={policyDetailModalOpen}
		{selectedPolicyDetails}
		on:close={() => policyDetailModalOpen = false}
	/>
	```
-->
<script lang="ts">
	import { createEventDispatcher } from 'svelte';
	import { t } from '$lib/stores/i18n';
	import { formatTimestampDMY } from '$lib/utils';
	import { flyAndScale } from '$lib/utils';
	import {
		Button,
		Modal,
		Tabs,
		TabItem,
		Badge,
	} from 'flowbite-svelte';
	import {
		ClipboardListOutline,
		ExclamationCircleOutline,
	} from 'flowbite-svelte-icons';

	// Props
	/** Whether the modal is open */
	export let isOpen = false;
	/** Raw policy details data from the TPA API workflow */
	export let selectedPolicyDetails: any = null;

	// Event dispatcher
	const dispatch = createEventDispatcher<{
		close: void;
	}>();

	// Tab state
	let activeTabValue = 'details';

	// Debug logging for props
	$: {
		console.log('PolicyDetailModal props changed:', {
			isOpen,
			hasSelectedPolicyDetails: !!selectedPolicyDetails,
			policyDetailsCount: selectedPolicyDetails?.step4_data?.ListOfPolDet?.length || 0,
			claimsCount: selectedPolicyDetails?.step4_data?.ListOfPolClaim?.length || 0
		});
	}

	// Modal transition parameters for smooth animations
	const modalTransitionParams = {
		y: -8,
		x: 0,
		start: 0.95,
		duration: 250
	};

	// Helper functions to extract data from raw API response
	function getPolicyInfo() {
		if (!selectedPolicyDetails?.step4_data?.ListOfPolDet?.length) return null;
		return selectedPolicyDetails.step4_data.ListOfPolDet[0];
	}

	function getClaims() {
		return selectedPolicyDetails?.step4_data?.ListOfPolClaim || [];
	}

	function getMemberInfo() {
		// Extract member info from the first claim or policy data
		const claims = getClaims();
		if (claims.length > 0) {
			return {
				name: claims[0].ClmMemberName || 'N/A',
				code: selectedPolicyDetails.member_code || 'N/A',
				policyNo: claims[0].ClmPolNo || 'N/A'
			};
		}
		return {
			name: 'N/A',
			code: selectedPolicyDetails?.member_code || 'N/A',
			policyNo: 'N/A'
		};
	}

	// Functions
	function closeModal() {
		console.log('PolicyDetailModal closing...');
		isOpen = false;
		dispatch('close');
		console.log('PolicyDetailModal close event dispatched');
	}
</script>

<!-- Policy Detail Modal -->
<Modal
	bind:open={isOpen}
	size="xl"
	autoclose
	transition={flyAndScale}
	params={modalTransitionParams}
	class="max-h-[90vh] overflow-y-auto bg-gray-100"
>
	<div slot="header" class="flex flex-col gap-1 space-y-1">
		<div class="flex items-center justify-between">
			<h2 class="flex items-center gap-3 text-lg font-semibold">
				<ClipboardListOutline class="h-6 w-6" />
				{t('policy_modal_header_title')}
				<span
					class="rounded-full border border-green-200 bg-green-100 px-3 py-1 text-xs font-medium text-green-800"
				>
					{t('policy_status_active')}
				</span>
			</h2>
		</div>
		{#if selectedPolicyDetails}
			{@const memberInfo = getMemberInfo()}
			<div class="flex flex-col gap-1 text-sm text-gray-600 sm:flex-row sm:gap-4">
				<span
					><strong>
						{t('policy_modal_header_member_name')}
					</strong>
					{memberInfo.name}</span
				>
				<span
					><strong>
						{t('policy_modal_header_member_code')}
					</strong>
					{memberInfo.code}</span
				>
				<span
					><strong>
						{t('policy_modal_info_policy_no')}
					</strong>
					{memberInfo.policyNo}</span
				>
			</div>
		{/if}
	</div>

	{#if selectedPolicyDetails}
		{@const policyDetails = selectedPolicyDetails.step4_data.ListOfPolDet || []}
		{@const claims = getClaims()}
		{@const memberInfo = getMemberInfo()}

		<div class="p-1">
			<!-- Tabbed Interface -->
			<Tabs style="underline" bind:activeTabValue={activeTabValue}>
				<!-- Policy Details Tab -->
				<TabItem open value="details" title="รายละเอียดกรมธรรม์">
					<svelte:fragment slot="title">
						<svg class="me-2 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
						</svg>
						รายละเอียดกรมธรรม์
					</svelte:fragment>

					<div class="space-y-4">
						<!-- Policy Information Section -->
						<div class="modal-content-section rounded-lg border border-gray-200 bg-white p-4">
							<h3 class="mb-3 text-lg font-semibold text-gray-700">
								ข้อมูลกรมธรรม์
							</h3>
							<div class="grid grid-cols-1 gap-3 sm:grid-cols-2">
								<div class="flex flex-col space-y-1 sm:flex-row sm:justify-between sm:space-y-0">
									<span class="text-sm text-gray-600">เลขที่กรมธรรม์</span>
									<span class="font-semibold text-gray-900">{memberInfo.policyNo}</span>
								</div>
								<div class="flex flex-col space-y-1 sm:flex-row sm:justify-between sm:space-y-0">
									<span class="text-sm text-gray-600">รหัสสมาชิก</span>
									<span class="font-semibold text-gray-900">{memberInfo.code}</span>
								</div>
								{#if claims.length > 0}
									<div class="flex flex-col space-y-1 sm:flex-row sm:justify-between sm:space-y-0">
										<span class="text-sm text-gray-600">บริษัทประกัน</span>
										<span class="font-semibold text-gray-900">{claims[0].ClmInsurerTH || 'N/A'}</span>
									</div>
									<div class="flex flex-col space-y-1 sm:flex-row sm:justify-between sm:space-y-0">
										<span class="text-sm text-gray-600">ประเภทบัตร</span>
										<span class="font-semibold text-gray-900">{claims[0].ClmCardType || 'N/A'}</span>
									</div>
								{/if}
							</div>
						</div>

						<!-- Benefits Coverage Section -->
						{#if policyDetails.length > 0}
							<div class="modal-content-section rounded-lg border border-gray-200 bg-white p-4">
								<div class="flex items-center justify-between mb-3">
									<h3 class="text-lg font-semibold text-gray-700">
										ความคุ้มครอง
									</h3>
									<span class="text-sm text-gray-600">(หน่วย: บาท)</span>
								</div>

								<div class="space-y-4">
									{#each policyDetails as benefit}
										<div class="rounded-lg border border-gray-200 p-4 shadow-sm">
											<h4 class="mb-3 text-lg font-semibold text-gray-900">
												{benefit.MainBenefit || benefit.MainBenefitEN}
											</h4>

											{#if benefit.Coverage && benefit.Coverage.length > 0}
												<div class="space-y-2">
													{#each benefit.Coverage as coverage}
														{#if coverage.CovDesc && coverage.CovDesc.trim()}
															<div class="flex flex-col gap-2 rounded-lg bg-gray-50 p-3 sm:flex-row sm:justify-between">
																<div class="flex-1">
																	<div class="font-medium text-gray-900">
																		{coverage.CovDesc}
																	</div>
																	{#if coverage.CovDescEN && coverage.CovDescEN !== coverage.CovDesc}
																		<div class="text-sm text-gray-600">
																			{coverage.CovDescEN}
																		</div>
																	{/if}
																</div>
																{#if coverage.CovLimit && coverage.CovLimit.trim()}
																	<div class="text-right">
																		<div class="text-sm text-gray-600">วงเงิน</div>
																		<div class="font-bold text-blue-600">
																			{coverage.CovLimit}
																		</div>
																		{#if coverage.CovUtilized && coverage.CovUtilized.trim() && coverage.CovUtilized !== '-'}
																			<div class="text-xs text-gray-500">
																				{coverage.CovUtilized}
																			</div>
																		{/if}
																	</div>
																{/if}
															</div>
														{/if}
													{/each}
												</div>
											{/if}
										</div>
									{/each}
								</div>
							</div>
						{/if}
					</div>
				</TabItem>

				<!-- Claims Tab -->
				<TabItem value="claims" title="ประวัติการเคลม">
					<svelte:fragment slot="title">
						<ExclamationCircleOutline class="me-2 h-4 w-4" />
						ประวัติการเคลม ({claims.length})
					</svelte:fragment>

					<div class="space-y-4">
						{#if claims.length === 0}
							<div class="flex flex-col items-center justify-center py-12 text-center">
								<div class="mb-4 rounded-full bg-gray-100 p-6">
									<ExclamationCircleOutline class="h-12 w-12 text-gray-400" />
								</div>
								<h3 class="mb-2 text-lg font-medium text-gray-900">ไม่มีประวัติการเคลม</h3>
								<p class="text-sm text-gray-500">ยังไม่มีการยื่นเคลมสำหรับกรมธรรม์นี้</p>
							</div>
						{:else}
							<!-- Claims Timeline -->
							<div class="modal-content-section rounded-lg border border-gray-200 bg-white p-4">
								<div class="mb-4 flex flex-col items-start justify-between sm:flex-row sm:items-center">
									<h3 class="mb-2 text-lg font-semibold text-gray-700 sm:mb-0">
										ประวัติการเคลม
									</h3>
									<div class="text-sm text-gray-600">
										รวม {claims.length} รายการ (หน่วย: บาท)
									</div>
								</div>

								<!-- Timeline Container -->
								<div class="relative">
									<!-- Timeline line -->
									<div class="absolute left-4 top-8 bottom-0 w-0.5 bg-gray-300"></div>

									<!-- Timeline Items -->
									<div class="space-y-6">
										{#each claims as claim}
											<div class="relative flex items-start">
												<!-- Timeline dot -->
												<div class="relative z-10 flex h-8 w-8 flex-shrink-0 items-center justify-center rounded-full border-4 border-white bg-white shadow-lg
													{claim.ClmStatus === 'Paid' || claim.ClmStatus === 'Closed'
														? 'ring-2 ring-green-500 bg-green-100'
														: claim.ClmStatus === 'Open' || claim.ClmStatus === 'Pending'
															? 'ring-2 ring-blue-500 bg-blue-100'
															: claim.ClmStatus === 'Rejected'
																? 'ring-2 ring-red-500 bg-red-100'
																: 'ring-2 ring-yellow-500 bg-yellow-100'}">
													<!-- Status icon -->
													{#if claim.ClmStatus === 'Paid' || claim.ClmStatus === 'Closed'}
														<svg class="h-4 w-4 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
															<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
														</svg>
													{:else if claim.ClmStatus === 'Open' || claim.ClmStatus === 'Pending'}
														<svg class="h-4 w-4 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
															<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z"></path>
														</svg>
													{:else if claim.ClmStatus === 'Rejected'}
														<svg class="h-4 w-4 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
															<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
														</svg>
													{:else}
														<svg class="h-4 w-4 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
															<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.082 16.5c-.77.833.192 2.5 1.732 2.5z"></path>
														</svg>
													{/if}
												</div>

												<!-- Timeline content -->
												<div class="ml-6 min-w-0 flex-1">
													<!-- Main claim card -->
													<div class="rounded-lg border border-gray-200 bg-white p-4 shadow-sm hover:shadow transition-shadow">
														<!-- Claim header -->
														<div class="mb-3 flex flex-col items-start justify-between lg:flex-row">
															<div class="mb-3 flex-1 lg:mb-0">
																<div class="mb-2 flex flex-col gap-2 sm:flex-row sm:items-center sm:gap-3">
																	<h4 class="text-lg font-semibold text-gray-900">
																		{claim.ClmNo}
																	</h4>
																	<div class="flex flex-wrap gap-2">
																		<!-- Status Badge -->
																		<Badge
																			color={claim.ClmStatus === 'Paid' || claim.ClmStatus === 'Closed'
																				? 'green'
																				: claim.ClmStatus === 'Open' || claim.ClmStatus === 'Pending'
																					? 'blue'
																					: claim.ClmStatus === 'Rejected'
																						? 'red'
																						: 'yellow'}
																			class="text-xs"
																		>
																			{claim.ClmStatusTxt || claim.ClmStatus}
																		</Badge>
																		<!-- Type Badge -->
																		<Badge color="dark" class="text-xs">
																			{claim.ClmType}
																		</Badge>
																		<!-- Source Badge -->
																		<Badge color="purple" class="text-xs">
																			{claim.ClmSource}
																		</Badge>
																	</div>
																</div>
																{#if claim.ClmDiagTH}
																	<p class="text-gray-700">{claim.ClmDiagTH}</p>
																{/if}
															</div>

															<!-- Amount Information -->
															<div class="flex flex-col gap-2 rounded-lg bg-gray-50 p-3 text-sm shadow-sm sm:flex-row sm:justify-between md:gap-6 lg:justify-end lg:gap-6">
																<div class="flex flex-col sm:text-right">
																	<div class="text-xs text-gray-600">จำนวนเงินเคลม</div>
																	<div class="text-xl font-bold text-gray-600">
																		{claim.ClmIncurredAmt ? Number(claim.ClmIncurredAmt).toLocaleString() : '0'}
																	</div>
																</div>
																<div class="flex flex-col sm:text-right">
																	<div class="text-xs text-gray-600">จำนวนที่จ่าย</div>
																	<div class="text-xl font-bold text-green-600">
																		{claim.ClmPayable ? Number(claim.ClmPayable).toLocaleString() : '0'}
																	</div>
																</div>
															</div>
														</div>

														<!-- Claim Details Grid -->
														<div class="mt-3 grid gap-3 text-sm sm:grid-cols-2 lg:grid-cols-3">
															<!-- Visit Date -->
															<div class="flex items-start">
																<svg class="mr-2 mt-0.5 h-4 w-4 flex-shrink-0 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
																	<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"/>
																</svg>
																<div>
																	<div class="font-medium text-gray-700">วันที่เข้ารับการรักษา</div>
																	<div class="text-gray-600">{claim.ClmVisitDate || '-'}</div>
																</div>
															</div>

															<!-- Payment Date -->
															{#if claim.ClmPaymentDate}
																<div class="flex items-start">
																	<svg class="mr-2 mt-0.5 h-4 w-4 flex-shrink-0 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
																		<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
																	</svg>
																	<div>
																		<div class="font-medium text-gray-700">วันที่จ่ายเงิน</div>
																		<div class="text-gray-600">{claim.ClmPaymentDate}</div>
																	</div>
																</div>
															{/if}

															<!-- Provider Information -->
															{#if claim.ClmProviderTH}
																<div class="flex items-start">
																	<svg class="mr-2 mt-0.5 h-4 w-4 flex-shrink-0 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
																		<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"/>
																	</svg>
																	<div>
																		<div class="font-medium text-gray-700">สถานพยาบาล</div>
																		<div class="text-gray-600">{claim.ClmProviderTH}</div>
																	</div>
																</div>
															{/if}
														</div>
													</div>
												</div>
											</div>
										{/each}
									</div>
								</div>
							</div>
						{/if}
					</div>
				</TabItem>
			</Tabs>
		</div>
	{/if}

	<svelte:fragment slot="footer">
		<div class="modal-footer-right">
			<Button color="light" class="ml-auto" on:click={closeModal}>
				{t('policy_modal_close')}
			</Button>
		</div>
	</svelte:fragment>
</Modal>

<style>
	/* Modal content animations with accessibility support */
	:global(.modal-content-section) {
		animation: fadeInUp 0.3s ease-out;
		animation-fill-mode: both;
	}

	:global(.modal-content-section:nth-child(1)) {
		animation-delay: 0.05s;
	}
	:global(.modal-content-section:nth-child(2)) {
		animation-delay: 0.1s;
	}
	:global(.modal-content-section:nth-child(3)) {
		animation-delay: 0.15s;
	}
	:global(.modal-content-section:nth-child(4)) {
		animation-delay: 0.2s;
	}
	:global(.modal-content-section:nth-child(5)) {
		animation-delay: 0.25s;
	}
	:global(.modal-content-section:nth-child(6)) {
		animation-delay: 0.3s;
	}

	@keyframes fadeInUp {
		from {
			opacity: 0;
			transform: translateY(20px);
		}
		to {
			opacity: 1;
			transform: translateY(0);
		}
	}

	/* Respect user's motion preferences */
	@media (prefers-reduced-motion: reduce) {
		:global(.modal-content-section) {
			animation: none;
		}

		@keyframes fadeInUp {
			from,
			to {
				opacity: 1;
				transform: translateY(0);
			}
		}
	}

	/* Modal footer button alignment - target the specific modal footer */
	:global(.modal-footer-right) {
		display: flex !important;
		justify-content: flex-end !important;
		width: 100% !important;
	}

	/* Additional targeting for Flowbite Modal footer */
	:global([data-modal-target] .modal-footer-right),
	:global(.fixed .modal-footer-right) {
		display: flex !important;
		justify-content: flex-end !important;
		width: 100% !important;
		margin-left: auto !important;
	}
</style>